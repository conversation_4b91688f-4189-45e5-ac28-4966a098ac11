import * as dotenv from 'dotenv';

dotenv.config();

interface TeamsChannelRequest {
  id: string;
  method: string;
  url: string;
}

const CHAT_SEARCH_SIZE: string = process.env['CHAT_SEARCH_SIZE'] ?? '50';
const CHAT_MAX_YEARS: string = process.env['CHAT_MAX_YEARS'] ?? '2';
const CHAT_SCHEDULE: string = process.env['CHAT_SCHEDULE'] ?? 'Daytime';

function getNighttimeRollingYearDateRange(): { start: string; end: string } {
  const today = new Date();
  
  const maxYears = parseInt(CHAT_MAX_YEARS, 10);
  const yearsToSubtract = isNaN(maxYears) || maxYears <= 0 ? 1 : maxYears;
  
  const startDate = new Date(today);
  startDate.setFullYear(today.getFullYear() - yearsToSubtract);
  startDate.setDate(today.getDate());
  const startStr = startDate.getFullYear() + '-' + 
                   String(startDate.getMonth() + 1).padStart(2, '0') + '-' + 
                   String(startDate.getDate()).padStart(2, '0');
  
  const endStr = today.getFullYear() + '-' + 
                 String(today.getMonth() + 1).padStart(2, '0') + '-' + 
                 String(today.getDate()).padStart(2, '0');
  return {
    start: startStr,
    end: endStr
  };
}

function getDaytimeRollingDateRange(): { start: string; end: string } {
  const now = new Date();
  
  const startDate = new Date(now);
  startDate.setFullYear(now.getFullYear() - 2); // Exactly 2 years ago
  
  return {
    start: startDate.toISOString(),
    end: now.toISOString()
  };
}

export function createChatsMessagesRequests(chatId: string): TeamsChannelRequest[] {
  if (!chatId) return [];

  let dateFilter: string;
  
  if (CHAT_SCHEDULE === 'Daytime') {
    const dateRange = getDaytimeRollingDateRange();
    dateFilter = `lastModifiedDateTime gt ${dateRange.start} and lastModifiedDateTime lt ${dateRange.end}`;
    
  } else if (CHAT_SCHEDULE === 'Nighttime') {
    const dateRange = getNighttimeRollingYearDateRange();
    dateFilter = `lastModifiedDateTime gt ${dateRange.start}T00:00:00Z and lastModifiedDateTime lt ${dateRange.end}T23:59:59Z`;

  } else {
    dateFilter = '';
  }

  return [{
    id: `${chatId}`,
    method: 'GET',
    url: `/chats/${chatId}/messages?$top=${CHAT_SEARCH_SIZE}&$filter=${dateFilter}`
  }];
}

export function createChatsMembersRequests(chatId: string): TeamsChannelRequest[] {
  if (!chatId) return [];

  return [{
    id: `${chatId}`,
    method: 'GET',
    url: `/chats/${chatId}/members`
  }];
}