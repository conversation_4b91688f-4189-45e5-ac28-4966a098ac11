@import '../../../../styles/variables';
@import '../../../../styles/mixin';

.simple-modal {
  display: flex;
  flex-flow: column;
  position: relative;

  &.is-self-height {
    height: 100%;
  }

  // 選択されたアイテムがある場合の高さ調整
  &.has-selected-items {
    @include media-pc {
      min-height: 600px;
      max-height: 80vh;
    }
  }

  @include media-pc {
    background-color: var(--color-guide-background-1);
    border-radius: 4px;
    box-shadow: $box-shadow-2;
  }
}

.simple-modal-edge {
  @include media-pc {
    display: none;
  }
}

.simple-modal-close-pc {
  position: absolute;
  top: 18px;
  right: 18px;
  z-index: 1;

  &:hover {
    .ui-icon__filled {
      fill: var(--color-guide-brand-icon-hover);
    }
  }

  @include media-sp {
    display: none;
  }
}

.simple-modal-scroll-wrapper {
  height: 100%;
  display: flex;
  flex-flow: column;
  box-shadow: $box-shadow-2;

  @include media-sp {
    background-color: var(--color-guide-background-1);
  }

  @include media-pc {
    border-radius: 4px;
    overflow: hidden;
    padding-top: 20px;
  }
}

.simple-modal-scroll-inner {
  display: flex;
  flex-flow: column;
  height: 100%;
  padding: 24px;

  @include media-pc {
    padding-top: 12px;
  }

  @include media-sp {
    padding-top: 12px;
    padding-left: var(--length-margin-horizontal-sp);
    padding-right: var(--length-margin-horizontal-sp);
  }
}

.simple-modal-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.simple-modal-title {
  font-size: 24px;
  margin: 0;

  @include media-sp {
    font-size: 16px;
  }
}

// インフォメーションボタンのスタイル
.teams-setting-info-button {
  color: var(--color-guide-brand-main-foreground) !important;
  padding: 4px !important;
  min-width: auto !important;

  .ui-icon {
    color: var(--color-guide-brand-main-foreground) !important;
    font-size: 16px;
  }

  &:hover {
    background-color: var(--color-guide-background-active) !important;

    .ui-icon {
      color: var(--color-guide-brand-icon-hover) !important;
    }
  }

  @include media-sp {
    padding: 6px !important;

    .ui-icon {
      font-size: 18px;
    }
  }
}

.simple-modal-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; // flexアイテムが縮小できるようにする

  p {
    font-size: 14px;
    line-height: 1.5;
    margin: 0 0 16px 0;
    color: var(--color-guide-foreground-1);
  }
}

// 説明文とインフォメーションボタンを横並びにするコンテナ
.simple-modal-description {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;

  p {
    margin: 0;
    flex: 1;
  }
}

// 検索入力フィールド
.simple-modal-chat-input {
  margin-bottom: 4px;

  .ui-input {
    width: 100%;

    // フォーカス時の下線色をsearchInputと同じ色に設定
    input:focus {
      border-color: var(--color-guide-brand-main-foreground);
    }
  }
}

// チャットアイテム一覧
.simple-modal-chat-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
  flex: 1;
  overflow-y: auto; // スクロールに自動変換される
  padding-right: 4px; // スクロールバーとの間隔
  min-height: 0; // flexアイテムが縮小できるようにする

  // スクロールバーのスタイリング（Webkit系ブラウザ）
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--color-guide-background-1);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--color-guide-foreground-3);
    border-radius: 4px;

    &:hover {
      background: var(--color-guide-foreground-2);
    }
  }
}

// 個別のチャットアイテム
.simple-modal-chat-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border: 1px solid var(--color-guide-deafult-border);
  border-radius: 4px;
  background-color: var(--color-guide-background-2);
  transition: background-color 0.2s ease;

  &:hover {
    background-color: var(--color-guide-background-active);
  }

  // 選択状態のスタイル
  &.selected {
    background-color: var(--color-guide-background-active);

    // 選択状態でもホバー色を維持
    &:hover {
      background-color: var(--color-guide-background-active);
    }
  }
}

// チャットアイテムのコンテンツ部分
.simple-modal-chat-item-content {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0; // flexアイテムが縮小できるようにする
  overflow: hidden; // はみ出しを防ぐ
}

// チャットアイテムのラベル
.simple-modal-chat-item-label {
  font-size: 14px;
  color: var(--color-guide-foreground-2);
  margin-right: 8px;
  flex-shrink: 0;
  white-space: nowrap;
}

// チャットアイテムの名前
.simple-modal-chat-item-name {
  font-size: 14px;
  color: var(--color-guide-foreground-1);
  font-weight: 500;
  flex: 1;
  overflow: hidden; // はみ出しを隠す
  white-space: nowrap; // 改行を防ぐ
  text-overflow: ellipsis; // ...で省略表示
  min-width: 0; // 縮小可能にする
}

// ローディング状態
.simple-modal-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32px 16px;

  p {
    font-size: 14px;
    color: var(--color-guide-foreground-2);
    margin: 0;
  }
}

// エラー状態
.simple-modal-error {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  background-color: var(--color-guide-error-background);
  border: 1px solid var(--color-guide-error-border);
  border-radius: 4px;
  margin-bottom: 16px;

  p {
    font-size: 14px;
    color: var(--color-guide-error-foreground);
    margin: 0;
  }
}

// 検索結果なし状態
.simple-modal-no-results {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32px 16px;

  p {
    font-size: 14px;
    color: var(--color-guide-foreground-2);
    margin: 0;
    text-align: center;
  }
}

// さらに読み込むボタン
.simple-modal-load-more {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  padding: 16px 0;
  border-top: 1px solid var(--color-guide-foreground-4);
  margin-top: 16px;

  .ui-button {
    min-width: 120px;
  }

  .load-more-info {
    text-align: center;

    span {
      font-size: 12px;
      color: var(--color-guide-foreground-2);
    }
  }
}

// ページネーション（後方互換性のため残す）
.simple-modal-pagination {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px 0;
  border-top: 1px solid var(--color-guide-foreground-4);
  margin-top: 16px;

  .pagination-info {
    text-align: center;

    span {
      font-size: 12px;
      color: var(--color-guide-foreground-2);
    }
  }

  .pagination-controls {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;

    .pagination-current {
      font-size: 14px;
      color: var(--color-guide-foreground-1);
      min-width: 60px;
      text-align: center;
    }

    .ui-button {
      min-width: 60px;
    }
  }
}

// 保存ボタンセクション
.simple-modal-save-section {
  .ui-button {
    background-color: var(--color-guide-main-brand-foreground-bg-force-light) !important;
    border-color: var(--color-guide-main-brand-foreground-bg-force-light) !important;
    color: white !important;

    &:hover {
      background-color: var(--color-guide-brand-foreground-2) !important;
      border-color: var(--color-guide-brand-foreground-2) !important;
    }

    &:disabled {
      background-color: var(--color-guide-foreground-disabled) !important;
      border-color: var(--color-guide-foreground-disabled) !important;
      color: var(--color-guide-foreground-2) !important;
    }

    // 保存完了時のスタイル（ゼラニウムカラーのまま）
    &.save-completed {
      background-color: var(--color-guide-main-brand-foreground-bg-force-light) !important;
      border-color: var(--color-guide-main-brand-foreground-bg-force-light) !important;
      color: white !important;
    }
  }
}

// 選択中アイテムカウント表示
.selected-items-count {
  margin-bottom: 8px;
  padding: 0 4px;
}

.selected-items-count-text {
  font-size: 12px;
  color: var(--color-guide-foreground-2);
  font-weight: 500;
}

// 無限スクロール用のローディング表示
.simple-modal-infinite-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px 20px;
  color: var(--color-guide-foreground-2);
  font-size: 14px;

  @include media-pc {
    display: none; // PCでは非表示
  }
}
